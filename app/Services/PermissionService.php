<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Role;
use App\Models\User;
use App\Traits\HasPermissionHelpers;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;

final class PermissionService
{
    use HasPermissionHelpers;

    /**
     * Create a new role.
     */
    public function createRole(string $name, string $guardName = 'api', ?int $organisationId = null): Role
    {
        $role = Role::firstOrCreate([
            'name' => $name,
            'guard_name' => $guardName,
            'organisation_id' => $organisationId,
        ]);

        return $role;
    }

    /**
     * Assign role to user within organisation context.
     */
    public function assignRoleToUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        // For system roles with different guard, we need to handle this specially
        if ($role->guard_name === 'system') {
            // Directly insert into model_has_roles table to bypass guard validation
            $user->roles()->attach($role->id, [
                'model_type' => get_class($user),
                'organisation_id' => $role->organisation_id
            ]);
        } else {
            $user->assignRole($role);
        }

        // Clear the cache to ensure role is recognized
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        return $user;
    }

    /**
     * Get user role names considering both system and organisation contexts.
     */
    public function getUserRoleNames(User $user): \Illuminate\Support\Collection
    {
        // Use direct database query to bypass Spatie Permission's team context filtering
        // This joins model_has_roles with roles table to get all role names for the user
        return \DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_type', get_class($user))
            ->where('model_has_roles.model_id', $user->id)
            ->pluck('roles.name')
            ->unique();
    }

    /**
     * Assign role to user with owner uniqueness check.
     */
    public function assignRoleToUserSafely(User $assigner, User $targetUser, Role $role): User
    {
        // Special validation for owner role - only one owner per organisation
        if ($role->name === 'owner' && $role->organisation_id) {
            // Set team context for the query
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

            $existingOwner = User::whereHas('roles', function ($query) use ($role) {
                $query->where('roles.name', 'owner')
                    ->where('roles.guard_name', 'api')
                    ->where('roles.organisation_id', $role->organisation_id);
            })->where('id', '!=', $targetUser->id)->first();

            if ($existingOwner) {
                throw ValidationException::withMessages([
                    'role' => ['该组织已经有一个所有者，请先转移所有者角色。'],
                ]);
            }
        }

        // Set appropriate team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);

        // For system roles with different guard, we need to handle this specially
        if ($role->guard_name === 'system') {
            // Directly insert into model_has_roles table to bypass guard validation
            $targetUser->roles()->attach($role->id, [
                'model_type' => get_class($targetUser),
                'organisation_id' => $role->organisation_id
            ]);
        } else {
            $targetUser->assignRole($role);
        }

        // Clear the cache to ensure role is recognized
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        return $targetUser;
    }

    /**
     * Transfer owner role from one user to another.
     */
    public function transferOwnerRole(User $currentOwner, User $newOwner, int $organisationId): bool
    {
        // Validate that current user is actually the owner
        $ownerRole = Role::where('name', 'owner')
            ->where('guard_name', 'api')
            ->where('organisation_id', $organisationId)
            ->first();

        if (!$ownerRole) {
            throw ValidationException::withMessages([
                'role' => ['组织所有者角色不存在。'],
            ]);
        }

        // Set team context
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);

        // Check if current owner has the role (considering they might be system admin)
        $currentOwnerHasRole = false;

        // Check if current owner is system admin (can transfer any owner role)
        $isSystemAdmin = $currentOwner->hasSystemAdminAccess();

        if ($isSystemAdmin) {
            $currentOwnerHasRole = true;
        } else {
            // Check if current owner actually has the owner role for this organisation
            $currentOwnerHasRole = $currentOwner->hasRole($ownerRole);
        }

        if (!$currentOwnerHasRole) {
            throw ValidationException::withMessages([
                'role' => ['当前用户不是该组织的所有者或系统管理员。'],
            ]);
        }

        // Verify new owner belongs to the organisation
        if (!$newOwner->belongsToOrganisation($organisationId)) {
            throw ValidationException::withMessages([
                'role' => ['新所有者必须属于指定的组织。'],
            ]);
        }

        // For current owner, they can be system admin transferring ownership
        if (!$currentOwner->hasSystemAdminAccess() && !$currentOwner->belongsToOrganisation($organisationId)) {
            throw ValidationException::withMessages([
                'role' => ['当前用户必须属于指定的组织或为系统管理员。'],
            ]);
        }

        // Remove role from current owner only if they actually have it
        if ($currentOwner->hasRole($ownerRole)) {
            $currentOwner->removeRole($ownerRole);
        }

        // Assign role to new owner
        // Owner role uses 'api' guard, so no special handling needed
        $newOwner->assignRole($ownerRole);

        return true;
    }

    /**
     * Get available roles that a user can assign.
     */
    public function getAssignableRoles(User $user): array
    {
        // Get user's highest role level
        $userHighestLevel = $this->getUserHighestRoleLevel($user);

        if ($userHighestLevel === 0) {
            // User has no roles, cannot assign anything
            return [];
        }

        // Get all role names with level lower than user's highest level
        // This is done purely from ROLE_HIERARCHY constant without database queries
        $assignableRoleNames = collect(self::ROLE_HIERARCHY)
            ->filter(fn($level) => $level < $userHighestLevel)
            ->keys()
            ->toArray();

        return $assignableRoleNames;
    }

    /**
     * Remove role from user within organisation context.
     */
    public function removeRoleFromUser(User $user, Role $role): User
    {
        // Set the team context to the role's organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($role->organisation_id);
        $user->removeRole($role);
        return $user;
    }

    /**
     * Get user's complete role information including both system and organisation roles.
     */
    public function getUserCompleteRoleInfo(User $user): array
    {
        // Get system-wide roles (clear team context first)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $systemRoles = $user->roles()
            ->where('roles.guard_name', 'system')
            ->whereNull('roles.organisation_id')
            ->get(['roles.id', 'roles.name', 'roles.organisation_id']);

        // Get organisation roles for all user's organisations
        $organisationRoles = collect();
        $userOrganisationIds = $user->getOrganisationIds();

        foreach ($userOrganisationIds as $organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            $orgRoles = $user->roles()
                ->where('roles.guard_name', 'api')
                ->where('roles.organisation_id', $organisationId)
                ->get(['roles.id', 'roles.name', 'roles.organisation_id']);
            $organisationRoles = $organisationRoles->merge($orgRoles);
        }

        return [
            'system_roles' => $systemRoles,
            'organisation_roles' => $organisationRoles,
            'all_role_names' => $systemRoles->pluck('name')->merge($organisationRoles->pluck('name'))->unique()->values(),
        ];
    }

    /**
     * Get all roles for a user within organisation context.
     */
    public function getUserRoles(User $user, ?int $organisationId = null): \Illuminate\Support\Collection
    {
        if ($organisationId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
            return $user->getRoleNames();
        }

        // Get roles from all user's organisations
        $allRoles = collect();
        $userOrganisationIds = $user->getOrganisationIds();

        foreach ($userOrganisationIds as $orgId) {
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($orgId);
            $orgRoles = $user->getRoleNames();
            $allRoles = $allRoles->merge($orgRoles);
        }

        return $allRoles->unique();
    }

    /**
     * Get all roles for organisation.
     */
    public function getOrganisationRoles(int $organisationId): Collection
    {
        return Role::where('organisation_id', $organisationId)->get();
    }

    /**
     * Sync user roles within organisation context.
     */
    public function syncUserRoles(User $user, array $roleNames, int $organisationId): User
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($organisationId);
        $user->syncRoles($roleNames);
        return $user;
    }

    /**
     * Create default roles for an organisation.
     * Note: Only creates organisation-specific roles (owner, member).
     * Global admin roles (root, admin) are created via createSystemRoles().
     */
    public function createDefaultRoles(int $organisationId): array
    {
        $roles = [
            'owner',          // 组织所有者
            'member',         // 成员
        ];

        $createdRoles = [];
        foreach ($roles as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'api',
                'organisation_id' => $organisationId,
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }

    /**
     * Create global system roles (not tied to any organisation).
     */
    public function createSystemRoles(): array
    {
        $systemRoles = [
            'root',           // Root - 系统初始化时设置的第一个管理员账户
            'admin',          // 管理员
        ];

        $createdRoles = [];
        foreach ($systemRoles as $roleName) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'system', // Use 'system' guard for global admin roles
                'organisation_id' => null, // System-wide roles
            ]);

            $createdRoles[] = $role;
        }

        return $createdRoles;
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Organisation;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample organisations
        $activeOrg = Organisation::factory()->active()->create([
            'name' => 'Tech Solutions Ltd',
            'code' => 'TECH001',
            'details' => [
                'industry' => 'Technology',
                'size' => 'Medium',
                'founded' => 2020,
                'website' => 'https://techsolutions.example.com',
            ],
            'remarks' => 'Leading technology solutions provider',
        ]);

        $pendingOrg = Organisation::factory()->pending()->create([
            'name' => 'Healthcare Innovations',
            'code' => 'HEALTH001',
            'details' => [
                'industry' => 'Healthcare',
                'size' => 'Large',
                'founded' => 2018,
            ],
            'remarks' => 'Innovative healthcare solutions',
        ]);

        $suspendedOrg = Organisation::factory()->suspended()->create([
            'name' => 'Finance Corp',
            'code' => 'FIN001',
            'details' => [
                'industry' => 'Finance',
                'size' => 'Small',
                'founded' => 2019,
            ],
            'remarks' => 'Financial services company - currently under review',
        ]);

        // Create test user with organisations
        $testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
        ]);
        $testUser->organisations()->attach([$activeOrg->id, $pendingOrg->id]);

        // Create additional users for different organisations
        $users1 = User::factory()->count(3)->create();
        foreach ($users1 as $user) {
            $user->organisations()->attach($activeOrg->id);
        }

        $users2 = User::factory()->count(2)->create();
        foreach ($users2 as $user) {
            $user->organisations()->attach($pendingOrg->id);
        }

        $user3 = User::factory()->create();
        $user3->organisations()->attach($suspendedOrg->id);

        // Create some users without organisation
        User::factory()->count(2)->create();
    }
}

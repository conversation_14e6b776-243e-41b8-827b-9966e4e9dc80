{"info": {"name": "JAST Partner API", "description": "Complete API collection for JAST Partner application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Status", "item": [{"name": "Global Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/health", "host": ["{{baseURL}}"], "path": ["api", "health"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has status field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "});"]}}]}, {"name": "API v1 Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/health", "host": ["{{baseURL}}"], "path": ["api", "v1", "health"]}}}, {"name": "API v1 Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/status", "host": ["{{baseURL}}"], "path": ["api", "v1", "status"]}}}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\",\n  \"password\": \"{{testPassword}}\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/login", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Login successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.token).to.exist;", "    ", "    // Store token for other requests", "    pm.environment.set(\"authToken\", jsonData.data.token);", "});"]}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/logout", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Logout successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    ", "    // Clear token after logout", "    pm.environment.set(\"authToken\", \"\");", "});"]}}]}, {"name": "Revoke All Tokens", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/auth/revoke-all-tokens", "host": ["{{baseURL}}"], "path": ["api", "v1", "auth", "revoke-all-tokens"]}}}]}, {"name": "User Management", "description": "User management endpoints with role-based access control. System admins (Root/Admin) can manage all users, while organization owners can only manage users within their organizations.", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/user", "host": ["{{baseURL}}"], "path": ["api", "v1", "user"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get current user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "});"]}}]}, {"name": "Get Users List", "description": "Get paginated list of users. System admins see all users, organization users see only users from their organizations.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get users list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.data).to.be.an('array');", "});"]}}]}, {"name": "Get Users by Organisation", "description": "Filter users by organization ID. Organization users can only filter by their own organizations.", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users?organisation_id={{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"], "query": [{"key": "organisation_id", "value": "{{organisationId}}"}]}}}, {"name": "Create User", "description": "Create a new user. System admins can create users in any organization, organization owners can only create users in their own organizations.", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users", "host": ["{{baseURL}}"], "path": ["api", "v1", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "    ", "    // Store user ID for other requests", "    pm.environment.set(\"userId\", jsonData.data.id);", "});"]}}]}, {"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get user details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.organisations).to.be.an('array');", "});"]}}]}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated User Name\",\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('Updated User Name');", "});"]}}]}, {"name": "Suspend User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/suspend", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "suspend"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Suspend user successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Activate User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/activate", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "activate"]}}}, {"name": "Add User to Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Add user to organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Remove User from Organisation", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Remove user from organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}, {"name": "Sync User Organisations", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"organisation_ids\": [{{organisationId}}]\n}"}, "url": {"raw": "{{baseURL}}/api/v1/users/{{userId}}/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "users", "{{userId}}", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Sync user organisations successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}]}, {"name": "Organisation Management", "item": [{"name": "Get Organisations List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get organisations list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.data).to.be.an('array');", "});"]}}]}, {"name": "Get Organisations by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations?status=active", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"], "query": [{"key": "status", "value": "active"}]}}}, {"name": "Create Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Organisation\",\n  \"code\": \"TEST001\",\n  \"details\": {\n    \"industry\": \"Technology\",\n    \"size\": \"Medium\"\n  },\n  \"remarks\": \"Test organisation for API testing\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.status).to.eql('pending');", "    ", "    // Store organisation ID for other requests", "    pm.environment.set(\"organisationId\", jsonData.data.id);", "});"]}}]}, {"name": "Get Organisation Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get organisation details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "});"]}}]}, {"name": "Update Organisation", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organisation Name\",\n  \"details\": {\n    \"industry\": \"Technology\",\n    \"size\": \"Large\"\n  },\n  \"remarks\": \"Updated remarks\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('Updated Organisation Name');", "});"]}}]}, {"name": "Suspend Organisation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseURL}}/api/v1/organisations/{{organisationId}}/suspend", "host": ["{{baseURL}}"], "path": ["api", "v1", "organisations", "{{organisationId}}", "suspend"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Suspend organisation successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.status).to.eql('suspended');", "    pm.expect(jsonData.data.is_suspended).to.eql(true);", "});"]}}]}]}, {"name": "Role Management", "item": [{"name": "Get Roles List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get roles list successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.be.an('array');", "});"]}}]}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"custom-role\",\n  \"guard_name\": \"api\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/roles", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Create role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.name).to.eql('custom-role');", "    ", "    // Store role ID for other requests", "    pm.environment.set(\"roleId\", jsonData.data.id);", "});"]}}]}, {"name": "Get Role Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Get role details successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.id).to.exist;", "});"]}}]}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated-custom-role\"\n}"}, "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Update role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data.name).to.eql('updated-custom-role');", "});"]}}]}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseURL}}/api/v1/roles/{{roleId}}", "host": ["{{baseURL}}"], "path": ["api", "v1", "roles", "{{roleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Delete role successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"]}}]}]}], "variable": [{"key": "baseURL", "value": "http://localhost"}, {"key": "authToken", "value": ""}, {"key": "testEmail", "value": "<EMAIL>"}, {"key": "testPassword", "value": "password"}, {"key": "organisationId", "value": ""}, {"key": "roleId", "value": ""}]}
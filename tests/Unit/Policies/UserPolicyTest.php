<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Policies\UserPolicy;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class UserPolicyTest extends TestCase
{
    use RefreshDatabase;

    private UserPolicy $policy;
    private PermissionService $permissionService;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;

    // System roles
    private Role $systemRootRole;
    private Role $systemAdminRole;

    // Organisation roles
    private Role $ownerRole;
    private Role $memberRole;
    private Role $anotherOwnerRole;
    private Role $anotherMemberRole;

    // Test users
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $anotherOwnerUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new UserPolicy();
        $this->permissionService = app(PermissionService::class);

        // Create test organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->systemRootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->systemAdminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles = $this->permissionService->createDefaultRoles($this->organisation->id);
        $this->ownerRole = collect($orgRoles)->firstWhere('name', 'owner');
        $this->memberRole = collect($orgRoles)->firstWhere('name', 'member');

        $anotherOrgRoles = $this->permissionService->createDefaultRoles($this->anotherOrganisation->id);
        $this->anotherOwnerRole = collect($anotherOrgRoles)->firstWhere('name', 'owner');
        $this->anotherMemberRole = collect($anotherOrgRoles)->firstWhere('name', 'member');

        // Create test users and assign roles
        $this->createTestUsers();
    }

    private function createTestUsers(): void
    {
        // Create system users (no organisation)
        $this->systemRootUser = User::factory()->create();
        $this->systemAdminUser = User::factory()->create();

        // Create organisation users
        $this->ownerUser = User::factory()->create();
        $this->ownerUser->organisations()->attach($this->organisation->id);

        $this->memberUser = User::factory()->create();
        $this->memberUser->organisations()->attach($this->organisation->id);

        $this->anotherOwnerUser = User::factory()->create();
        $this->anotherOwnerUser->organisations()->attach($this->anotherOrganisation->id);

        // Create user with no organisation
        $this->regularUser = User::factory()->create();

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->guard_name = 'system';
        $this->systemRootUser->assignRole($this->systemRootRole);

        $this->systemAdminUser->guard_name = 'system';
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->anotherOrganisation->id);
        $this->anotherOwnerUser->assignRole($this->anotherOwnerRole);
    }

    /**
     * Helper method to clear team context for system role checks.
     */
    private function clearTeamContext(): void
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_view_any_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_view_any_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_view_any_allows_organisation_owner(): void
    {
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
    }

    public function test_view_any_allows_organisation_member(): void
    {
        $this->assertTrue($this->policy->viewAny($this->memberUser));
    }

    public function test_view_any_denies_user_without_organisation(): void
    {
        $this->assertFalse($this->policy->viewAny($this->regularUser));
    }

    // ========================================
    // view Tests
    // ========================================

    public function test_view_allows_system_root_to_view_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->anotherOwnerUser));
        $this->assertTrue($this->policy->view($this->systemRootUser, $this->regularUser));
    }

    public function test_view_allows_system_admin_to_view_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->anotherOwnerUser));
        $this->assertTrue($this->policy->view($this->systemAdminUser, $this->regularUser));
    }

    public function test_view_allows_users_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->view($this->ownerUser, $this->memberUser));
        $this->assertTrue($this->policy->view($this->memberUser, $this->ownerUser));
    }

    public function test_view_denies_users_in_different_organisations(): void
    {
        $this->assertFalse($this->policy->view($this->ownerUser, $this->anotherOwnerUser));
        $this->assertFalse($this->policy->view($this->memberUser, $this->anotherOwnerUser));
    }

    public function test_view_denies_user_without_organisation(): void
    {
        $this->assertFalse($this->policy->view($this->regularUser, $this->ownerUser));
        $this->assertFalse($this->policy->view($this->ownerUser, $this->regularUser));
    }

    // ========================================
    // create Tests
    // ========================================

    public function test_create_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemRootUser));
    }

    public function test_create_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->create($this->systemAdminUser));
    }

    public function test_create_allows_organisation_owner(): void
    {
        $this->assertTrue($this->policy->create($this->ownerUser));
    }

    public function test_create_allows_organisation_member(): void
    {
        $this->assertTrue($this->policy->create($this->memberUser));
    }

    public function test_create_denies_user_without_organisation(): void
    {
        $this->assertFalse($this->policy->create($this->regularUser));
    }

    // ========================================
    // update Tests
    // ========================================

    public function test_update_allows_system_root_to_update_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->update($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_update_allows_system_admin_to_update_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->update($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_update_allows_users_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->update($this->ownerUser, $this->memberUser));
        $this->assertTrue($this->policy->update($this->memberUser, $this->ownerUser));
    }

    public function test_update_denies_users_in_different_organisations(): void
    {
        $this->assertFalse($this->policy->update($this->ownerUser, $this->anotherOwnerUser));
        $this->assertFalse($this->policy->update($this->memberUser, $this->anotherOwnerUser));
    }

    // ========================================
    // suspend Tests
    // ========================================

    public function test_suspend_allows_system_root_to_suspend_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->suspend($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->suspend($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_suspend_allows_system_admin_to_suspend_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->suspend($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->suspend($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_suspend_allows_users_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->suspend($this->ownerUser, $this->memberUser));
        $this->assertTrue($this->policy->suspend($this->memberUser, $this->ownerUser));
    }

    public function test_suspend_denies_users_in_different_organisations(): void
    {
        $this->assertFalse($this->policy->suspend($this->ownerUser, $this->anotherOwnerUser));
        $this->assertFalse($this->policy->suspend($this->memberUser, $this->anotherOwnerUser));
    }

    // ========================================
    // activate Tests
    // ========================================

    public function test_activate_allows_system_root_to_activate_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->activate($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->activate($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_activate_allows_system_admin_to_activate_any_user(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->activate($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->activate($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_activate_allows_users_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->activate($this->ownerUser, $this->memberUser));
        $this->assertTrue($this->policy->activate($this->memberUser, $this->ownerUser));
    }

    public function test_activate_denies_users_in_different_organisations(): void
    {
        $this->assertFalse($this->policy->activate($this->ownerUser, $this->anotherOwnerUser));
        $this->assertFalse($this->policy->activate($this->memberUser, $this->anotherOwnerUser));
    }

    // ========================================
    // addToOrganisation Tests
    // ========================================

    public function test_add_to_organisation_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->addToOrganisation($this->systemRootUser, $this->regularUser, $this->organisation->id));
        $this->assertTrue($this->policy->addToOrganisation($this->systemRootUser, $this->regularUser, $this->anotherOrganisation->id));
    }

    public function test_add_to_organisation_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->addToOrganisation($this->systemAdminUser, $this->regularUser, $this->organisation->id));
        $this->assertTrue($this->policy->addToOrganisation($this->systemAdminUser, $this->regularUser, $this->anotherOrganisation->id));
    }

    public function test_add_to_organisation_allows_user_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->addToOrganisation($this->ownerUser, $this->regularUser, $this->organisation->id));
        $this->assertTrue($this->policy->addToOrganisation($this->memberUser, $this->regularUser, $this->organisation->id));
    }

    public function test_add_to_organisation_denies_user_in_different_organisation(): void
    {
        $this->assertFalse($this->policy->addToOrganisation($this->ownerUser, $this->regularUser, $this->anotherOrganisation->id));
        $this->assertFalse($this->policy->addToOrganisation($this->memberUser, $this->regularUser, $this->anotherOrganisation->id));
    }

    public function test_add_to_organisation_denies_user_without_organisation(): void
    {
        $this->assertFalse($this->policy->addToOrganisation($this->regularUser, $this->ownerUser, $this->organisation->id));
    }

    // ========================================
    // removeFromOrganisation Tests
    // ========================================

    public function test_remove_from_organisation_allows_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->removeFromOrganisation($this->systemRootUser, $this->ownerUser, $this->organisation->id));
        $this->assertTrue($this->policy->removeFromOrganisation($this->systemRootUser, $this->anotherOwnerUser, $this->anotherOrganisation->id));
    }

    public function test_remove_from_organisation_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->removeFromOrganisation($this->systemAdminUser, $this->ownerUser, $this->organisation->id));
        $this->assertTrue($this->policy->removeFromOrganisation($this->systemAdminUser, $this->anotherOwnerUser, $this->anotherOrganisation->id));
    }

    public function test_remove_from_organisation_allows_user_in_same_organisation(): void
    {
        $this->assertTrue($this->policy->removeFromOrganisation($this->ownerUser, $this->memberUser, $this->organisation->id));
        $this->assertTrue($this->policy->removeFromOrganisation($this->memberUser, $this->ownerUser, $this->organisation->id));
    }

    public function test_remove_from_organisation_denies_user_in_different_organisation(): void
    {
        $this->assertFalse($this->policy->removeFromOrganisation($this->ownerUser, $this->anotherOwnerUser, $this->anotherOrganisation->id));
        $this->assertFalse($this->policy->removeFromOrganisation($this->memberUser, $this->anotherOwnerUser, $this->anotherOrganisation->id));
    }

    public function test_remove_from_organisation_denies_user_without_organisation(): void
    {
        $this->assertFalse($this->policy->removeFromOrganisation($this->regularUser, $this->ownerUser, $this->organisation->id));
    }

    // ========================================
    // syncOrganisations Tests
    // ========================================

    public function test_sync_organisations_allows_system_root(): void
    {
        $this->clearTeamContext();
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertTrue($this->policy->syncOrganisations($this->systemRootUser, $this->regularUser, $organisationIds));
    }

    public function test_sync_organisations_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertTrue($this->policy->syncOrganisations($this->systemAdminUser, $this->regularUser, $organisationIds));
    }

    public function test_sync_organisations_allows_user_with_access_to_all_organisations(): void
    {
        // User belongs to organisation, can sync to same organisation
        $organisationIds = [$this->organisation->id];
        $this->assertTrue($this->policy->syncOrganisations($this->ownerUser, $this->regularUser, $organisationIds));
    }

    public function test_sync_organisations_denies_user_without_access_to_all_organisations(): void
    {
        // User belongs to organisation but tries to sync to another organisation
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertFalse($this->policy->syncOrganisations($this->ownerUser, $this->regularUser, $organisationIds));
    }

    public function test_sync_organisations_allows_empty_organisation_list(): void
    {
        $this->assertTrue($this->policy->syncOrganisations($this->ownerUser, $this->regularUser, []));
        $this->assertTrue($this->policy->syncOrganisations($this->regularUser, $this->ownerUser, []));
    }

    // ========================================
    // manageOrganisations Tests
    // ========================================

    public function test_manage_organisations_allows_system_root(): void
    {
        $this->clearTeamContext();
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertTrue($this->policy->manageOrganisations($this->systemRootUser, $organisationIds));
    }

    public function test_manage_organisations_allows_system_admin(): void
    {
        $this->clearTeamContext();
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertTrue($this->policy->manageOrganisations($this->systemAdminUser, $organisationIds));
    }

    public function test_manage_organisations_allows_user_with_access_to_all_organisations(): void
    {
        $organisationIds = [$this->organisation->id];
        $this->assertTrue($this->policy->manageOrganisations($this->ownerUser, $organisationIds));
    }

    public function test_manage_organisations_denies_user_without_access_to_all_organisations(): void
    {
        $organisationIds = [$this->organisation->id, $this->anotherOrganisation->id];
        $this->assertFalse($this->policy->manageOrganisations($this->ownerUser, $organisationIds));
    }

    public function test_manage_organisations_denies_user_without_organisation(): void
    {
        $organisationIds = [$this->organisation->id];
        $this->assertFalse($this->policy->manageOrganisations($this->regularUser, $organisationIds));
    }

    public function test_manage_organisations_allows_empty_organisation_list(): void
    {
        $this->assertTrue($this->policy->manageOrganisations($this->ownerUser, []));
        $this->assertFalse($this->policy->manageOrganisations($this->regularUser, []));
    }

    // ========================================
    // delete Tests
    // ========================================

    public function test_delete_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->delete($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_delete_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->delete($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_delete_denies_organisation_users(): void
    {
        $this->assertFalse($this->policy->delete($this->ownerUser, $this->memberUser));
        $this->assertFalse($this->policy->delete($this->memberUser, $this->ownerUser));
    }

    public function test_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->delete($this->regularUser, $this->ownerUser));
    }

    // ========================================
    // restore Tests
    // ========================================

    public function test_restore_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->restore($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_restore_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->restore($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_restore_denies_organisation_users(): void
    {
        $this->assertFalse($this->policy->restore($this->ownerUser, $this->memberUser));
        $this->assertFalse($this->policy->restore($this->memberUser, $this->ownerUser));
    }

    public function test_restore_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->restore($this->regularUser, $this->ownerUser));
    }

    // ========================================
    // forceDelete Tests
    // ========================================

    public function test_force_delete_allows_only_system_root(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->ownerUser));
        $this->assertTrue($this->policy->forceDelete($this->systemRootUser, $this->anotherOwnerUser));
    }

    public function test_force_delete_allows_only_system_admin(): void
    {
        $this->clearTeamContext();
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->ownerUser));
        $this->assertTrue($this->policy->forceDelete($this->systemAdminUser, $this->anotherOwnerUser));
    }

    public function test_force_delete_denies_organisation_users(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->ownerUser, $this->memberUser));
        $this->assertFalse($this->policy->forceDelete($this->memberUser, $this->ownerUser));
    }

    public function test_force_delete_denies_regular_user(): void
    {
        $this->assertFalse($this->policy->forceDelete($this->regularUser, $this->ownerUser));
    }
}
